import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";

const LoginTest = () => {
  const { isLoggedIn, user, login, logout } = useAuth();

  const handleTestLogin = () => {
    login("<EMAIL>", "Test User");
  };

  const handleTestLogout = () => {
    logout();
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Authentication Test</h3>
      
      <div className="space-y-2 mb-4">
        <p><strong>Status:</strong> {isLoggedIn ? "Logged In" : "Logged Out"}</p>
        {user && (
          <>
            <p><strong>Email:</strong> {user.email}</p>
            <p><strong>Name:</strong> {user.name || "Not provided"}</p>
          </>
        )}
      </div>

      <div className="space-x-2">
        {!isLoggedIn ? (
          <Button onClick={handleTestLogin} className="bg-green-600 hover:bg-green-700">
            Test Login
          </Button>
        ) : (
          <Button onClick={handleTestLogout} variant="outline">
            Test Logout
          </Button>
        )}
      </div>
    </div>
  );
};

export default LoginTest;
