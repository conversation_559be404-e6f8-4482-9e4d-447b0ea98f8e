version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: preply-postgres
    environment:
      POSTGRES_DB: preply_auth
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - preply-network

  auth-service:
    build: .
    container_name: preply-auth-service
    environment:
      DB_USERNAME: postgres
      DB_PASSWORD: password
      JWT_SECRET: mySecretKey123456789012345678901234567890
      CORS_ORIGINS: http://localhost:3000,http://localhost:8081
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - preply-network

volumes:
  postgres_data:

networks:
  preply-network:
    driver: bridge
