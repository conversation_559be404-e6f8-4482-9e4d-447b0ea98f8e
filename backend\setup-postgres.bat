@echo off
echo 🐘 PostgreSQL Setup for Preply Auth Service
echo ==========================================

echo.
echo This script will help you set up PostgreSQL for the auth service.
echo.

REM Check if PostgreSQL is installed
where psql >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL is not installed or not in PATH
    echo.
    echo Please install PostgreSQL 16 from:
    echo https://www.postgresql.org/download/windows/
    echo.
    echo After installation, add PostgreSQL bin directory to your PATH:
    echo Example: C:\Program Files\PostgreSQL\16\bin
    pause
    exit /b 1
)

echo ✅ PostgreSQL is available

echo.
echo 📋 We will create:
echo    - Database: preply_auth
echo    - User: postgres (default)
echo.

set /p pg_password="Enter PostgreSQL password for 'postgres' user: "

echo.
echo 🔨 Creating database...

REM Create database
echo CREATE DATABASE preply_auth; | psql -U postgres -h localhost
if %errorlevel% equ 0 (
    echo ✅ Database 'preply_auth' created successfully
) else (
    echo ⚠️  Database might already exist or creation failed
)

echo.
echo 🧪 Testing connection...
echo SELECT 1 as test; | psql -U postgres -h localhost -d preply_auth
if %errorlevel% equ 0 (
    echo ✅ Connection to 'preply_auth' database successful
) else (
    echo ❌ Connection failed
    pause
    exit /b 1
)

echo.
echo 📝 Setting environment variables for this session...
set DB_USERNAME=postgres
set DB_PASSWORD=%pg_password%
set JWT_SECRET=mySecretKey123456789012345678901234567890

echo.
echo ✅ PostgreSQL setup complete!
echo.
echo 📋 Database Configuration:
echo    Host: localhost
echo    Port: 5432
echo    Database: preply_auth
echo    Username: postgres
echo.
echo 🚀 You can now run the auth service with:
echo    start-manual.bat
echo.
echo 💡 To make environment variables permanent:
echo    1. Press Win+R, type 'sysdm.cpl', press Enter
echo    2. Click 'Environment Variables'
echo    3. Add these variables:
echo       DB_USERNAME=postgres
echo       DB_PASSWORD=%pg_password%
echo       JWT_SECRET=mySecretKey123456789012345678901234567890

pause
