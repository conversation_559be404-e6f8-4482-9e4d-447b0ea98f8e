import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { X, ChevronLeft, ChevronRight, Sun } from "lucide-react";
import { format, addDays, startOfWeek, addWeeks, subWeeks } from "date-fns";

interface BookingDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: (bookingDetails: any) => void;
  tutor: {
    id: number;
    name: string;
    avatar: string;
  };
  selectedDuration: number;
}

const BookingDetailsModal = ({ isOpen, onClose, onContinue, tutor, selectedDuration }: BookingDetailsModalProps) => {
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [duration, setDuration] = useState(selectedDuration);

  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 }); // Monday start
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  const timeSlots = [
    "13:30", "14:00", "14:30", "15:00", "15:30"
  ];

  const handleContinue = () => {
    if (selectedDate && selectedTime) {
      onContinue({
        tutor,
        duration,
        date: selectedDate,
        time: selectedTime
      });
    }
  };

  const goToPreviousWeek = () => {
    setCurrentWeek(subWeeks(currentWeek, 1));
  };

  const goToNextWeek = () => {
    setCurrentWeek(addWeeks(currentWeek, 1));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg p-0 gap-0 max-h-[90vh] overflow-y-auto">
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <img
                src={tutor.avatar}
                alt={tutor.name}
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <DialogTitle className="text-lg font-semibold text-gray-900">
                  Book a trial lesson
                </DialogTitle>
                <p className="text-sm text-gray-600">
                  To discuss your level and learning plan
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-6 w-6 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="p-6 space-y-6">
          {/* Duration Selection */}
          <div className="flex gap-2">
            <Button
              variant={duration === 25 ? "default" : "outline"}
              onClick={() => setDuration(25)}
              className="flex-1"
            >
              25 mins
            </Button>
            <Button
              variant={duration === 50 ? "default" : "outline"}
              onClick={() => setDuration(50)}
              className="flex-1"
            >
              50 mins
            </Button>
          </div>

          {/* Week Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="icon"
              onClick={goToPreviousWeek}
              className="h-8 w-8"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <h3 className="font-semibold text-gray-900">
              {format(weekStart, "MMM d")} – {format(addDays(weekStart, 6), "d, yyyy")}
            </h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={goToNextWeek}
              className="h-8 w-8"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Calendar Week View */}
          <div className="grid grid-cols-7 gap-2">
            {weekDays.map((day, index) => {
              const dayNames = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
              const isSelected = selectedDate && format(day, "yyyy-MM-dd") === format(selectedDate, "yyyy-MM-dd");
              const isToday = format(day, "yyyy-MM-dd") === format(new Date(), "yyyy-MM-dd");

              return (
                <div key={index} className="text-center">
                  <div className="text-xs text-gray-500 mb-1">
                    {dayNames[index]}
                  </div>
                  <Button
                    variant={isSelected ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setSelectedDate(day)}
                    className={`w-full h-10 ${isSelected ? "bg-pink-500 hover:bg-pink-600" : ""} ${
                      isToday && !isSelected ? "border-pink-500" : ""
                    }`}
                  >
                    {format(day, "d")}
                  </Button>
                </div>
              );
            })}
          </div>

          {/* Timezone */}
          <p className="text-sm text-gray-600">
            In your time zone, Asia/Kolkata (GMT +5:30)
          </p>

          {/* Time Slots */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Sun className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium text-gray-700">Afternoon</span>
            </div>
            <div className="grid grid-cols-3 gap-2">
              {timeSlots.map((time) => (
                <Button
                  key={time}
                  variant={selectedTime === time ? "default" : "outline"}
                  onClick={() => setSelectedTime(time)}
                  className={`${
                    selectedTime === time ? "bg-pink-500 hover:bg-pink-600" : ""
                  }`}
                >
                  {time}
                </Button>
              ))}
            </div>
          </div>
        </div>

        <div className="px-6 pb-6">
          <Button
            onClick={handleContinue}
            disabled={!selectedDate || !selectedTime}
            className="w-full bg-gray-300 hover:bg-gray-400 text-gray-700 disabled:bg-gray-200 disabled:text-gray-400"
          >
            Continue
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BookingDetailsModal;
