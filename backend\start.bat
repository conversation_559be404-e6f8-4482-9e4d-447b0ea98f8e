@echo off
echo Starting Preply Authentication Service...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Docker is not installed. Please install Docker Desktop first.
    echo Download from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Docker Compose is not available. Please ensure Docker Desktop is running.
    pause
    exit /b 1
)

REM Start the services
echo Starting PostgreSQL and Auth Service...
docker-compose up -d

REM Wait for services to be ready
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose ps | findstr "Up" >nul
if %errorlevel% equ 0 (
    echo.
    echo ✅ Services started successfully!
    echo.
    echo 🚀 Auth Service is running at: http://localhost:8080/api
    echo 📚 Swagger UI is available at: http://localhost:8080/api/swagger-ui.html
    echo 🐘 PostgreSQL is running at: localhost:5432
    echo.
    echo 📖 Setup Guide: See SETUP_AND_TESTING_GUIDE.md
    echo 📖 API Documentation: See API_DOCUMENTATION.md
    echo.
    echo 🧪 Quick Test:
    echo    1. Open: http://localhost:8080/api/swagger-ui.html
    echo    2. Try the /auth/register endpoint
    echo    3. Use the returned token for /auth/me endpoint
    echo.
    echo To stop the services, run: docker-compose down
) else (
    echo ❌ Failed to start services. Check the logs with: docker-compose logs
    pause
    exit /b 1
)

pause
