@echo off
echo 🧪 Simple API Test for Online Learning Platform
echo ===============================================

set BASE_URL=http://localhost:8081/api

echo.
echo 📋 Testing API endpoints...
echo Base URL: %BASE_URL%

echo.
echo 1️⃣ Testing Registration...
powershell -Command "try { $response = Invoke-RestMethod -Uri '%BASE_URL%/auth/register' -Method Post -ContentType 'application/json' -Body '{\"full_name\": \"Test User\", \"email\": \"<EMAIL>\", \"password\": \"password123\"}'; Write-Host '✅ Registration successful!'; Write-Host 'User:' $response.user.full_name '(' $response.user.email ')'; $global:token = $response.access_token; Write-Host 'Token:' $global:token.Substring(0, 50) '...' } catch { Write-Host '❌ Registration failed:' $_.Exception.Message }"

echo.
echo 2️⃣ Testing Login...
powershell -Command "try { $response = Invoke-RestMethod -Uri '%BASE_URL%/auth/login' -Method Post -ContentType 'application/json' -Body '{\"email\": \"<EMAIL>\", \"password\": \"password123\"}'; Write-Host '✅ Login successful!'; Write-Host 'User:' $response.user.full_name; $global:token = $response.access_token; Write-Host 'New Token:' $global:token.Substring(0, 50) '...' } catch { Write-Host '❌ Login failed:' $_.Exception.Message }"

echo.
echo 3️⃣ Testing Protected Endpoint...
powershell -Command "try { $headers = @{ 'Authorization' = 'Bearer ' + $global:token }; $response = Invoke-RestMethod -Uri '%BASE_URL%/auth/me' -Method Get -Headers $headers; Write-Host '✅ Protected endpoint works!'; Write-Host 'Current User:' $response.full_name '(' $response.email ')' } catch { Write-Host '❌ Protected endpoint failed:' $_.Exception.Message }"

echo.
echo 🎉 API Testing Complete!
echo.
echo 📚 You can also test manually at:
echo    Swagger UI: %BASE_URL%/swagger-ui.html
echo.
pause
