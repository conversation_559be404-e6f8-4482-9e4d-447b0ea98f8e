# 🚀 Quick Start Guide - H2 Database Setup

## ⚡ Fastest Way to Run the Auth Service

This setup uses H2 in-memory database - **no PostgreSQL installation required!**

### Prerequisites
- **Java 17+** (Download from: https://adoptium.net/)
- **Maven 3.6+** (Download from: https://maven.apache.org/download.cgi)

### 🎯 One-Command Start

```cmd
cd backend
quick-start.bat
```

That's it! The service will:
1. Build the application
2. Start with H2 database
3. Be ready for testing

### 📚 Access Points

Once running, you can access:

- **🔗 Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **🗄️ H2 Database Console**: http://localhost:8080/api/h2-console
- **🌐 API Base URL**: http://localhost:8080/api

### 🗄️ H2 Database Console

To inspect the database:
1. Go to: http://localhost:8080/api/h2-console
2. Use these credentials:
   - **JDBC URL**: `jdbc:h2:mem:testdb`
   - **User Name**: `sa`
   - **Password**: `password`
3. Click "Connect"

### 🧪 Quick API Test

```cmd
test-quick.bat
```

This will test:
- User registration
- User login
- Save responses to JSON files

### 📋 Manual Testing Steps

#### 1. Register a User
**POST** `/auth/register`
```json
{
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 2. Copy Access Token
From the registration response, copy the `access_token`

#### 3. Test Protected Endpoint
**GET** `/auth/me`
- Add Authorization header: `Bearer YOUR_ACCESS_TOKEN`

### 🎨 Using Swagger UI

1. **Open**: http://localhost:8080/api/swagger-ui.html
2. **Register**: Try the `/auth/register` endpoint
3. **Authorize**: Click the 🔒 button, enter `Bearer YOUR_TOKEN`
4. **Test**: Try the `/auth/me` endpoint

### 🔧 Configuration

The application is configured with:
- **Port**: 8080
- **Context Path**: `/api`
- **Database**: H2 in-memory
- **JWT Secret**: Auto-generated
- **CORS**: Enabled for localhost:3000 and localhost:8081

### 📊 Database Schema

The H2 database will automatically create this table:

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL
);
```

### 🔄 Restart Process

To restart the application:
1. Press `Ctrl+C` to stop
2. Run `quick-start.bat` again

**Note**: H2 is in-memory, so all data is lost when you restart.

### 🚀 Production Setup

For production, switch to PostgreSQL:
1. Update `application.properties`
2. Change database configuration
3. Use `start-manual.bat` or Docker setup

### 🆘 Troubleshooting

#### Java not found
```cmd
# Check Java installation
java -version

# If not found, install from: https://adoptium.net/
```

#### Maven not found
```cmd
# Check Maven installation
mvn -version

# If not found, install from: https://maven.apache.org/download.cgi
```

#### Port 8080 in use
```cmd
# Find what's using port 8080
netstat -ano | findstr :8080

# Kill the process (replace PID)
taskkill /PID <PID> /F
```

#### Build fails
```cmd
# Clean and rebuild
mvn clean package -DskipTests
```

### 📈 Performance

H2 Database benefits:
- ✅ **Fast startup** (no external database)
- ✅ **No configuration** required
- ✅ **Perfect for development** and testing
- ✅ **Built-in web console**
- ⚠️ **Data lost on restart** (in-memory)

### 🎯 Next Steps

1. **Test all endpoints** using Swagger UI
2. **Inspect database** using H2 console
3. **Integrate with frontend** application
4. **Switch to PostgreSQL** for production

### 📞 Support

If you encounter issues:
1. Check `TROUBLESHOOTING.md`
2. Verify Java and Maven installation
3. Ensure port 8080 is available
4. Check application logs in console

Happy coding! 🎉
