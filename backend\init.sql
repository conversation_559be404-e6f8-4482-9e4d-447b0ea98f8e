-- Initialize Online Learning Platform Database
-- This script runs when PostgreSQL container starts for the first time

-- Create database if it doesn't exist (this is handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS online_learning;

-- Use the database
\c online_learning;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- The tables will be created automatically by Hibernate/JPA
-- This file is mainly for any initial data or custom configurations

-- You can add initial data here if needed
-- For example:
-- INSERT INTO users (id, full_name, email, password_hash, role, created_at, updated_at) 
-- VALUES (uuid_generate_v4(), 'Admin User', '<EMAIL>', '$2a$10$...', 'ADMIN', NOW(), NOW());

-- Grant permissions (optional, postgres user already has full access)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Print success message
\echo 'Database online_learning initialized successfully!'
