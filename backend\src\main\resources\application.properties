# Server Configuration
server.port=8081
server.servlet.context-path=/api

# Application Configuration
spring.application.name=auth-service

# Database Configuration (PostgreSQL)
spring.datasource.url=************************************************
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.open-in-view=false

# Jackson Configuration
spring.jackson.property-naming-strategy=SNAKE_CASE
spring.jackson.default-property-inclusion=NON_NULL

# Logging Configuration
logging.level.com.preply=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# JWT Configuration
jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
jwt.access-token-expiration=900000
jwt.refresh-token-expiration=604800000

# CORS Configuration
cors.allowed-origins=${CORS_ORIGINS:http://localhost:3000,http://localhost:8081}
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# Logging Configuration (Reduce verbose output for development)
logging.level.org.springframework.security=ERROR
logging.level.org.hibernate=ERROR
logging.level.com.zaxxer.hikari=ERROR
logging.level.org.springframework.web=ERROR
logging.level.org.springframework.boot.autoconfigure=ERROR
logging.level.org.springframework.web.servlet=ERROR
logging.level.org.springdoc=ERROR
logging.level.org.springframework.web.servlet.DispatcherServlet=ERROR
logging.level.org.springframework.web.servlet.mvc.method.annotation=ERROR

# Development Settings
spring.main.banner-mode=console
spring.output.ansi.enabled=always

# Disable default security password generation
spring.security.user.name=disabled
spring.security.user.password=disabled

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
