# Authentication Service API Documentation

## Base URL
```
http://localhost:8080/api
```

## Endpoints

### 1. User Registration
**POST** `/auth/register`

**Request Body:**
```json
{
  "full_name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "full_name": "<PERSON> Doe",
    "email": "<EMAIL>",
    "role": "USER",
    "created_at": "2023-12-01T10:00:00",
    "updated_at": "2023-12-01T10:00:00"
  }
}
```

### 2. User Login
**POST** `/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "role": "USER",
    "created_at": "2023-12-01T10:00:00",
    "updated_at": "2023-12-01T10:00:00"
  }
}
```

### 3. Get Current User
**GET** `/auth/me`

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response (200 OK):**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "role": "USER",
  "created_at": "2023-12-01T10:00:00",
  "updated_at": "2023-12-01T10:00:00"
}
```

### 4. Refresh Token
**POST** `/auth/refresh-token`

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response (200 OK):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "role": "USER",
    "created_at": "2023-12-01T10:00:00",
    "updated_at": "2023-12-01T10:00:00"
  }
}
```

## Error Responses

### Validation Error (400 Bad Request)
```json
{
  "timestamp": "2023-12-01T10:00:00",
  "status": 400,
  "error": "Validation Error",
  "message": "Invalid input data",
  "path": "/auth/register",
  "details": [
    "Email should be valid",
    "Password must be between 8 and 100 characters"
  ]
}
```

### Authentication Error (400 Bad Request)
```json
{
  "timestamp": "2023-12-01T10:00:00",
  "status": 400,
  "error": "Authentication Error",
  "message": "Invalid email or password",
  "path": "/auth/login"
}
```

### Unauthorized (403 Forbidden)
```json
{
  "timestamp": "2023-12-01T10:00:00",
  "status": 403,
  "error": "Forbidden",
  "message": "Access Denied",
  "path": "/auth/me"
}
```

## Token Information

- **Access Token**: Valid for 15 minutes, used for API authentication
- **Refresh Token**: Valid for 7 days, used to obtain new access tokens
- **Token Type**: Always "Bearer"

## User Roles

- **USER**: Regular user
- **TUTOR**: Tutor user
- **ADMIN**: Administrator user
