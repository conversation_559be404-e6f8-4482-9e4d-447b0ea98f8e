# 🧪 API Testing Guide

## 🚀 Quick Start

```cmd
# Start the application (clean output)
start-clean.bat

# Test the API automatically
test-api-simple.bat
```

## 🌐 Access Points

- **Swagger UI**: http://localhost:8081/api/swagger-ui.html
- **API Base**: http://localhost:8081/api

## 📋 Manual API Testing

### 1. Register a User

**POST** `/auth/register`

```json
{
  "full_name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Expected Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "full_name": "<PERSON>",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 2. Login

**POST** `/auth/login`

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### 3. Get Current User (Protected)

**GET** `/auth/me`

**Headers:**
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 4. Refresh Token

**POST** `/auth/refresh-token`

```json
{
  "refresh_token": "YOUR_REFRESH_TOKEN"
}
```

## 🧪 Testing Methods

### Method 1: Swagger UI (Recommended)
1. Go to: http://localhost:8081/api/swagger-ui.html
2. Test each endpoint interactively
3. Use "Authorize" button for protected endpoints

### Method 2: Automated Script
```cmd
test-api-simple.bat
```

### Method 3: PowerShell Commands
```powershell
# Register
$response = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/register" -Method Post -ContentType "application/json" -Body '{"full_name": "Test User", "email": "<EMAIL>", "password": "password123"}'

# Login
$loginResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/login" -Method Post -ContentType "application/json" -Body '{"email": "<EMAIL>", "password": "password123"}'

# Get current user
$headers = @{ "Authorization" = "Bearer " + $loginResponse.access_token }
$userResponse = Invoke-RestMethod -Uri "http://localhost:8081/api/auth/me" -Method Get -Headers $headers
```

## ✅ Expected Behaviors

### Registration
- ✅ New user created successfully
- ✅ Returns JWT tokens
- ❌ Duplicate email rejected
- ❌ Invalid email format rejected

### Login
- ✅ Valid credentials return tokens
- ❌ Invalid credentials rejected
- ❌ Non-existent user rejected

### Protected Endpoints
- ✅ Valid token allows access
- ❌ No token returns 403 Forbidden
- ❌ Invalid token returns 401 Unauthorized

### Data Persistence
- ✅ User data survives application restart
- ✅ Can login after restart
- ✅ PostgreSQL stores data permanently

## 🔧 Troubleshooting

### Application won't start
- Check PostgreSQL is running
- Verify port 8081 is available
- Check database connection

### API returns errors
- Verify request format (JSON)
- Check required fields
- Validate email format
- Ensure unique email for registration

### Database issues
- Confirm PostgreSQL service is running
- Check database 'online_learning' exists
- Verify connection credentials

## 🎯 Success Criteria

Your API is working correctly when:
- ✅ All endpoints respond without errors
- ✅ Registration creates new users
- ✅ Login returns valid tokens
- ✅ Protected endpoints work with tokens
- ✅ Data persists after restart
- ✅ Swagger UI loads and functions

Happy testing! 🚀
