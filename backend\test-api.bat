@echo off
setlocal enabledelayedexpansion

echo 🧪 Testing Preply Authentication API
echo ==================================

set BASE_URL=http://localhost:8080/api

REM Check if service is running
echo.
echo 1. Checking if service is running...
curl -s "%BASE_URL%/swagger-ui.html" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Service is running
) else (
    echo ❌ Service is not running. Please start it first with: start.bat
    pause
    exit /b 1
)

REM Test 1: Register a new user
echo.
echo 2. Testing user registration...
curl -s -X POST "%BASE_URL%/auth/register" ^
  -H "Content-Type: application/json" ^
  -d "{\"full_name\": \"Test User\", \"email\": \"<EMAIL>\", \"password\": \"password123\"}" ^
  -o register_response.json

findstr "access_token" register_response.json >nul
if %errorlevel% equ 0 (
    echo ✅ User registration successful
    REM Extract tokens (simplified for Windows batch)
    for /f "tokens=2 delims=:," %%a in ('findstr "access_token" register_response.json') do (
        set ACCESS_TOKEN=%%a
        set ACCESS_TOKEN=!ACCESS_TOKEN:"=!
    )
    for /f "tokens=2 delims=:," %%a in ('findstr "refresh_token" register_response.json') do (
        set REFRESH_TOKEN=%%a
        set REFRESH_TOKEN=!REFRESH_TOKEN:"=!
    )
    echo Access Token: !ACCESS_TOKEN:~0,50!...
) else (
    echo ❌ User registration failed
    type register_response.json
)

REM Test 2: Login with the same user
echo.
echo 3. Testing user login...
curl -s -X POST "%BASE_URL%/auth/login" ^
  -H "Content-Type: application/json" ^
  -d "{\"email\": \"<EMAIL>\", \"password\": \"password123\"}" ^
  -o login_response.json

findstr "access_token" login_response.json >nul
if %errorlevel% equ 0 (
    echo ✅ User login successful
) else (
    echo ❌ User login failed
    type login_response.json
)

REM Test 3: Get current user (protected endpoint)
echo.
echo 4. Testing protected endpoint (/auth/me)...
if defined ACCESS_TOKEN (
    curl -s -X GET "%BASE_URL%/auth/me" ^
      -H "Authorization: Bearer !ACCESS_TOKEN!" ^
      -o me_response.json
    
    findstr "<EMAIL>" me_response.json >nul
    if %errorlevel% equ 0 (
        echo ✅ Protected endpoint access successful
    ) else (
        echo ❌ Protected endpoint access failed
        type me_response.json
    )
) else (
    echo ❌ Skipping protected endpoint test (no access token)
)

REM Test 4: Invalid login
echo.
echo 5. Testing invalid login...
curl -s -X POST "%BASE_URL%/auth/login" ^
  -H "Content-Type: application/json" ^
  -d "{\"email\": \"<EMAIL>\", \"password\": \"wrongpassword\"}" ^
  -o invalid_login_response.json

findstr "Invalid email or password" invalid_login_response.json >nul
if %errorlevel% equ 0 (
    echo ✅ Invalid login properly rejected
) else (
    echo ❌ Invalid login test failed
    type invalid_login_response.json
)

REM Test 5: Duplicate registration
echo.
echo 6. Testing duplicate registration...
curl -s -X POST "%BASE_URL%/auth/register" ^
  -H "Content-Type: application/json" ^
  -d "{\"full_name\": \"Test User 2\", \"email\": \"<EMAIL>\", \"password\": \"password123\"}" ^
  -o duplicate_response.json

findstr "Email already registered" duplicate_response.json >nul
if %errorlevel% equ 0 (
    echo ✅ Duplicate registration properly rejected
) else (
    echo ❌ Duplicate registration test failed
    type duplicate_response.json
)

REM Cleanup
del register_response.json login_response.json me_response.json invalid_login_response.json duplicate_response.json 2>nul

echo.
echo 🎉 API Testing Complete!
echo ==================================
echo 📚 Open Swagger UI for interactive testing:
echo    http://localhost:8080/api/swagger-ui.html
echo.
echo 📖 For more testing options, see:
echo    SETUP_AND_TESTING_GUIDE.md

pause
