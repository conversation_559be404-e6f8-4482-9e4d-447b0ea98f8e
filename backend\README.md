# Authentication Microservice

A production-ready authentication microservice built with Spring Boot 3 and PostgreSQL.

## Features

- User registration and login
- JWT-based authentication (access + refresh tokens)
- Spring Security with BCrypt password hashing
- Input validation with structured error responses
- PostgreSQL database integration
- Comprehensive unit tests

## Tech Stack

- Java 17+
- Spring Boot 3
- Spring Security
- Spring Data JPA
- PostgreSQL 16
- JWT (JSON Web Tokens)
- Maven
- JUnit 5

## API Endpoints

- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user (secured)
- `POST /auth/refresh-token` - Refresh access token

## Database Schema

Users table with fields:
- id (UUID)
- full_name
- email
- password_hash
- role
- created_at
- updated_at

## Setup

1. Ensure PostgreSQL 16 is running
2. Update database configuration in `application.yaml`
3. Run `mvn spring-boot:run`

## Testing

Run tests with: `mvn test`
