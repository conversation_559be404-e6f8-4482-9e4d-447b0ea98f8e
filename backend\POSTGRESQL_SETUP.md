# 🐘 PostgreSQL Setup for Online Learning Platform

## 📋 Database Configuration

- **Host**: localhost
- **Port**: 5432
- **Database**: online_learning
- **Username**: postgres
- **Password**: password (default)

## 🚀 Setup Options

### Option 1: Using Docker (Recommended)

```bash
# Start PostgreSQL with Docker Compose
docker-compose up postgres -d

# Verify it's running
docker ps
```

### Option 2: Local PostgreSQL Installation

1. **Download PostgreSQL**: https://www.postgresql.org/download/
2. **Install PostgreSQL** with default settings
3. **Run setup script**:
   ```cmd
   setup-postgresql.bat
   ```

### Option 3: Manual Setup

```sql
-- Connect to PostgreSQL as postgres user
psql -U postgres

-- Create database
CREATE DATABASE online_learning;

-- Exit
\q
```

## 🔧 Environment Variables

You can customize database connection using environment variables:

```bash
# Windows
set DB_USERNAME=postgres
set DB_PASSWORD=your_password

# Linux/Mac
export DB_USERNAME=postgres
export DB_PASSWORD=your_password
```

## 🚀 Start Your Application

```cmd
cd backend
mvn clean install
mvn spring-boot:run
```

## ✅ Verify Setup

1. **Check PostgreSQL is running**:
   ```cmd
   psql -h localhost -p 5432 -U postgres -l
   ```

2. **Check database exists**:
   ```sql
   \c online_learning
   \dt
   ```

3. **Start application and check logs**:
   - Look for: "Tomcat started on port 8081"
   - No database connection errors

## 🧪 Test Your Setup

- **Swagger UI**: http://localhost:8081/api/swagger-ui.html
- **Register a user** and check if data is saved in PostgreSQL

## 🔍 Troubleshooting

### PostgreSQL not running
```cmd
# Windows (if installed as service)
net start postgresql-x64-16

# Check if port 5432 is in use
netstat -an | findstr :5432
```

### Connection refused
- Verify PostgreSQL is running
- Check firewall settings
- Ensure port 5432 is not blocked

### Authentication failed
- Check username/password
- Verify pg_hba.conf allows local connections

## 📊 Database Schema

The application will automatically create these tables:
- `users` - User accounts and authentication data

Tables are created automatically by Hibernate when the application starts.

## 🎉 Success!

Once setup is complete, your application will use PostgreSQL for persistent data storage!
