# 🚀 Setup and Testing Guide

## Prerequisites

Before running the application, ensure you have the following installed:

- **Java 17+** (OpenJDK or Oracle JDK)
- **Maven 3.6+**
- **Docker & Docker Compose** (for easy setup)
- **PostgreSQL 16** (if running without Docker)

## 🐳 Option 1: Quick Start with Docker (Recommended)

### Step 1: Clone and Navigate
```bash
cd backend
```

### Step 2: Start Services
```bash
# Make the script executable
chmod +x start.sh

# Start PostgreSQL and Auth Service
./start.sh
```

### Step 3: Verify Services
```bash
# Check if services are running
docker-compose ps

# View logs
docker-compose logs auth-service
```

## 💻 Option 2: Manual Setup (Local Development)

### Step 1: Setup PostgreSQL
```bash
# Install PostgreSQL 16
# Create database
createdb preply_auth

# Or using psql
psql -U postgres
CREATE DATABASE preply_auth;
\q
```

### Step 2: Configure Environment Variables
```bash
export DB_USERNAME=postgres
export DB_PASSWORD=your_password
export JWT_SECRET=mySecretKey123456789012345678901234567890
```

### Step 3: Build and Run
```bash
# Build the application
mvn clean package -DskipTests

# Run the application
mvn spring-boot:run

# Or run the JAR file
java -jar target/auth-service-1.0.0.jar
```

## 🧪 Testing the API

### 1. Access Swagger UI
Open your browser and navigate to:
```
http://localhost:8080/api/swagger-ui.html
```

### 2. API Endpoints Overview
- **Base URL**: `http://localhost:8080/api`
- **Swagger UI**: `http://localhost:8080/api/swagger-ui.html`
- **API Docs**: `http://localhost:8080/api/v3/api-docs`

### 3. Test Flow with Swagger UI

#### Step 1: Register a New User
1. Click on **"Authentication"** section
2. Click on **"POST /auth/register"**
3. Click **"Try it out"**
4. Use this sample request:
```json
{
  "full_name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123"
}
```
5. Click **"Execute"**
6. Copy the `access_token` from the response

#### Step 2: Test Protected Endpoint
1. Click on **"GET /auth/me"**
2. Click **"Try it out"**
3. Click the **"Authorize"** button (🔒 icon)
4. Enter: `Bearer YOUR_ACCESS_TOKEN`
5. Click **"Authorize"**
6. Click **"Execute"**

#### Step 3: Test Login
1. Click on **"POST /auth/login"**
2. Use the same credentials:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Step 4: Test Token Refresh
1. Copy the `refresh_token` from login response
2. Click on **"POST /auth/refresh-token"**
3. Use:
```json
{
  "refresh_token": "YOUR_REFRESH_TOKEN"
}
```

## 🔧 Testing with cURL

### Register User
```bash
curl -X POST "http://localhost:8080/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "Jane Doe",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Login
```bash
curl -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Get Current User (Protected)
```bash
curl -X GET "http://localhost:8080/api/auth/me" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### Refresh Token
```bash
curl -X POST "http://localhost:8080/api/auth/refresh-token" \
  -H "Content-Type: application/json" \
  -d '{
    "refresh_token": "YOUR_REFRESH_TOKEN"
  }'
```

## 🧪 Running Unit Tests

```bash
# Run all tests
mvn test

# Run tests with coverage
mvn test jacoco:report

# Run specific test class
mvn test -Dtest=AuthControllerTest
```

## 📊 Health Checks

### Application Health
```bash
curl http://localhost:8080/api/actuator/health
```

### Database Connection
```bash
# Check if PostgreSQL is accessible
docker-compose exec postgres psql -U postgres -d preply_auth -c "SELECT 1;"
```

## 🐛 Troubleshooting

### Common Issues

1. **Port 8080 already in use**
   ```bash
   # Change port in application.properties
   server.port=8081
   ```

2. **Database connection failed**
   ```bash
   # Check PostgreSQL is running
   docker-compose ps postgres
   
   # Check logs
   docker-compose logs postgres
   ```

3. **JWT token issues**
   ```bash
   # Ensure JWT_SECRET is set and long enough (32+ characters)
   echo $JWT_SECRET
   ```

### Logs
```bash
# View application logs
docker-compose logs -f auth-service

# View PostgreSQL logs
docker-compose logs -f postgres
```

## 🔄 Stopping Services

```bash
# Stop Docker services
docker-compose down

# Remove volumes (clears database)
docker-compose down -v
```

## 📈 Performance Testing

### Load Testing with Apache Bench
```bash
# Test registration endpoint
ab -n 100 -c 10 -T 'application/json' \
  -p register.json \
  http://localhost:8080/api/auth/register
```

Where `register.json` contains:
```json
{
  "full_name": "Test User",
  "email": "<EMAIL>",
  "password": "password123"
}
```

## 🎯 Next Steps

1. **Frontend Integration**: Use the API endpoints in your React application
2. **Production Deployment**: Configure production database and environment variables
3. **Monitoring**: Add application monitoring and logging
4. **Security**: Implement rate limiting and additional security measures

Happy testing! 🎉
