@echo off
echo 🧪 Quick API Test - Preply Auth Service
echo ======================================

set BASE_URL=http://localhost:8081/api

echo.
echo 1. Checking if service is running...
curl -s "%BASE_URL%/swagger-ui.html" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Service is running
) else (
    echo ❌ Service is not running. Please start it first with: quick-start.bat
    pause
    exit /b 1
)

echo.
echo 2. Testing user registration...
curl -s -X POST "%BASE_URL%/auth/register" ^
  -H "Content-Type: application/json" ^
  -d "{\"full_name\": \"Test User\", \"email\": \"<EMAIL>\", \"password\": \"password123\"}" ^
  -o register_response.json

findstr "access_token" register_response.json >nul
if %errorlevel% equ 0 (
    echo ✅ User registration successful
    echo Response saved to register_response.json
) else (
    echo ❌ User registration failed
    echo Response:
    type register_response.json
)

echo.
echo 3. Testing user login...
curl -s -X POST "%BASE_URL%/auth/login" ^
  -H "Content-Type: application/json" ^
  -d "{\"email\": \"<EMAIL>\", \"password\": \"password123\"}" ^
  -o login_response.json

findstr "access_token" login_response.json >nul
if %errorlevel% equ 0 (
    echo ✅ User login successful
    echo Response saved to login_response.json
) else (
    echo ❌ User login failed
    echo Response:
    type login_response.json
)

echo.
echo 🎉 Quick test complete!
echo.
echo 📚 Next steps:
echo    1. Open Swagger UI: http://localhost:8080/api/swagger-ui.html
echo    2. View H2 Database: http://localhost:8080/api/h2-console
echo    3. Check response files: register_response.json, login_response.json
echo.
echo 💡 For interactive testing, use Swagger UI
echo    You can copy tokens from response files to test protected endpoints

pause
