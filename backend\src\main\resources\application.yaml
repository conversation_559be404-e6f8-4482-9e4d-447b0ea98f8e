# Server Configuration
server.port=8080
server.servlet.context-path=/api

# Application Configuration
spring.application.name=auth-service

# Database Configuration
spring.datasource.url=************************************************
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:root}
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Jackson Configuration
spring.jackson.property-naming-strategy=SNAKE_CASE
spring.jackson.default-property-inclusion=NON_NULL

# Logging Configuration
logging.level.com.preply=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# JWT Configuration
jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
jwt.access-token-expiration=900000
jwt.refresh-token-expiration=604800000

# CORS Configuration
cors.allowed-origins=${CORS_ORIGINS:http://localhost:3000,http://localhost:8081}
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true
