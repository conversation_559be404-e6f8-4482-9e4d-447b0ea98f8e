# Authentication Microservice

A production-ready authentication microservice built with Spring Boot 3, PostgreSQL, and JWT-based authentication.

## Features

- User registration and login
- JWT-based authentication with access and refresh tokens
- Password hashing using BCrypt
- Input validation and structured error responses
- PostgreSQL database with Flyway migrations
- Comprehensive unit tests
- Production-ready configuration

## Tech Stack

- **Java 17+**
- **Spring Boot 3.2.0**
- **Spring Security**
- **Spring Data JPA**
- **PostgreSQL**
- **Flyway** (Database migrations)
- **JWT (JJWT)**
- **Maven**

## API Endpoints

### Public Endpoints
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh-token` - Refresh access token

### Protected Endpoints
- `GET /api/v1/auth/me` - Get current user information

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'USER',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### Refresh Tokens Table
```sql
CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_HOST` | Database host | localhost |
| `DB_PORT` | Database port | 5432 |
| `DB_NAME` | Database name | authdb |
| `DB_USERNAME` | Database username | postgres |
| `DB_PASSWORD` | Database password | password |
| `JWT_SECRET` | JWT signing secret | mySecretKey... |
| `JWT_ACCESS_EXPIRATION` | Access token expiration (ms) | 900000 (15 min) |
| `JWT_REFRESH_EXPIRATION` | Refresh token expiration (ms) | 604800000 (7 days) |

## Getting Started

### Prerequisites
- Java 17 or higher
- Maven 3.6+
- PostgreSQL 12+

### Setup Database
```sql
CREATE DATABASE authdb;
CREATE USER authuser WITH PASSWORD 'authpass';
GRANT ALL PRIVILEGES ON DATABASE authdb TO authuser;
```

### Run the Application
```bash
# Clone the repository
git clone <repository-url>
cd auth-service

# Set environment variables
export DB_HOST=localhost
export DB_PORT=5432
export DB_NAME=authdb
export DB_USERNAME=authuser
export DB_PASSWORD=authpass

# Run the application
mvn spring-boot:run
```

### Run Tests
```bash
mvn test
```

## API Usage Examples

### Register a User
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Login
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Get Current User
```bash
curl -X GET http://localhost:8080/api/v1/auth/me \
  -H "Authorization: Bearer <access-token>"
```

### Refresh Token
```bash
curl -X POST http://localhost:8080/api/v1/auth/refresh-token \
  -H "Content-Type: application/json" \
  -d '{
    "refreshToken": "<refresh-token>"
  }'
```

## Response Examples

### Successful Registration/Login
```json
{
  "userId": "123e4567-e89b-12d3-a456-426614174000",
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "role": "USER",
  "accessToken": "eyJhbGciOiJIUzUxMiJ9...",
  "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
  "expiresIn": 900000
}
```

### Error Response
```json
{
  "message": "Email is already taken!",
  "status": 409,
  "error": "User Already Exists",
  "timestamp": "2023-12-01T10:30:00",
  "path": "/api/v1/auth/register"
}
```

## Security Features

- Password hashing with BCrypt
- JWT tokens with configurable expiration
- Refresh token rotation
- Input validation
- CORS support
- SQL injection prevention
- Automatic cleanup of expired tokens

## Production Deployment

1. Set up PostgreSQL database
2. Configure environment variables
3. Build the application: `mvn clean package`
4. Run with: `java -jar target/auth-service-1.0.0.jar`

## Health Checks

The application exposes health check endpoints:
- `GET /actuator/health` - Application health status
- `GET /actuator/info` - Application information

## License

This project is licensed under the MIT License.
