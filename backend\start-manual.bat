@echo off
echo 🚀 Starting Preply Auth Service (Manual Setup)
echo ===============================================

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed or not in PATH
    echo Please install Java 17+ from: https://adoptium.net/
    pause
    exit /b 1
)

REM Check if <PERSON><PERSON> is installed
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven is not installed or not in PATH
    echo Please install Maven from: https://maven.apache.org/download.cgi
    pause
    exit /b 1
)

echo ✅ Java and Maven are available

REM Set default environment variables if not set
if not defined DB_USERNAME set DB_USERNAME=postgres
if not defined DB_PASSWORD set DB_PASSWORD=password
if not defined JWT_SECRET set JWT_SECRET=mySecretKey123456789012345678901234567890

echo.
echo 📋 Configuration:
echo    Database URL: ********************************************
echo    Database User: %DB_USERNAME%
echo    JWT Secret: %JWT_SECRET:~0,20%...

echo.
echo ⚠️  IMPORTANT: Make sure PostgreSQL is running on localhost:5432
echo    and database 'preply_auth' exists.
echo.

set /p continue="Continue? (y/n): "
if /i not "%continue%"=="y" (
    echo Cancelled by user
    pause
    exit /b 0
)

echo.
echo 🔨 Building application...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo 🚀 Starting application...
echo    Access at: http://localhost:8080/api
echo    Swagger UI: http://localhost:8080/api/swagger-ui.html
echo.
echo Press Ctrl+C to stop the application
echo.

REM Start the application
java -jar target\auth-service-1.0.0.jar

pause
