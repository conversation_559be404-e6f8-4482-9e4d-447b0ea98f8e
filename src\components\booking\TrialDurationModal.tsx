import { useState } from "react";
import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, Zap, Calendar } from "lucide-react";

interface TrialDurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: (duration: number) => void;
}

const TrialDurationModal = ({ isOpen, onClose, onContinue }: TrialDurationModalProps) => {
  const [selectedDuration, setSelectedDuration] = useState<number | null>(null);

  const handleContinue = () => {
    if (selectedDuration) {
      onContinue(selectedDuration);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-0 gap-0">
        <DialogHeader className="p-6 pb-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold text-gray-900">
              Which trial duration would you prefer?
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-6 w-6 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="px-6 pb-6 space-y-4">
          {/* 25 mins option */}
          <div
            className={`border rounded-lg p-4 cursor-pointer transition-all ${
              selectedDuration === 25
                ? "border-pink-500 bg-pink-50"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => setSelectedDuration(25)}
          >
            <div className="flex items-start gap-3">
              <div className="mt-1">
                <Zap className="h-5 w-5 text-gray-700" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">25 mins</h3>
                <p className="text-sm text-gray-600">
                  Get to know the tutor, discuss your goals and learning plan
                </p>
              </div>
            </div>
          </div>

          {/* 50 mins option */}
          <div
            className={`border rounded-lg p-4 cursor-pointer transition-all ${
              selectedDuration === 50
                ? "border-pink-500 bg-pink-50"
                : "border-gray-200 hover:border-gray-300"
            }`}
            onClick={() => setSelectedDuration(50)}
          >
            <div className="flex items-start gap-3">
              <div className="mt-1">
                <Calendar className="h-5 w-5 text-gray-700" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-1">50 mins</h3>
                <p className="text-sm text-gray-600">
                  Get everything that's in a 25-min lesson plus start learning
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 pb-6">
          <Button
            onClick={handleContinue}
            disabled={!selectedDuration}
            className="w-full bg-gray-300 hover:bg-gray-400 text-gray-700 disabled:bg-gray-200 disabled:text-gray-400"
          >
            Continue
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TrialDurationModal;
