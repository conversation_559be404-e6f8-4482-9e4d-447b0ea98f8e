@echo off
echo 🚀 Starting Online Learning Authentication Service
echo ==================================================

echo.
echo 📋 Configuration:
echo    Database: PostgreSQL (localhost:5432/online_learning)
echo    Port: 8081
echo    Context Path: /api
echo    Mode: Development (Clean Output)

echo.
echo 🔧 Building application...
mvn clean package -DskipTests -q

if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

echo ✅ Build successful!

echo.
echo 🚀 Starting application...
echo.
echo 📚 Once started, you can access:
echo    • Swagger UI: http://localhost:8081/api/swagger-ui.html
echo    • API Base: http://localhost:8081/api
echo.
echo 🧪 To test API quickly, run: test-api-simple.bat
echo.

java -jar target/auth-service-1.0.0.jar
