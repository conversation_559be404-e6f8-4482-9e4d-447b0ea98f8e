# 🚀 Start Your Backend (Port 8081)

## **Simple Commands**

```cmd
cd backend
mvn clean install
mvn spring-boot:run
```

## **What You'll See**

When successful, you'll see:
```
Started AuthServiceApplication in X.X seconds
Tomcat started on port 8081 (http) with context path '/api'
```

## **Test Your Backend**

Open in browser:
- **Swagger UI**: http://localhost:8081/api/swagger-ui.html
- **H2 Database**: http://localhost:8081/api/h2-console
- **Test Page**: file:///D:/Sanket/Outschool%20Demo/learn-like-preply-clone/backend/test-api.html

## **Quick API Test**

1. Go to: http://localhost:8081/api/swagger-ui.html
2. Try "POST /auth/register" with:
```json
{
  "full_name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```
3. Copy the access_token
4. Click "Authorize" and enter: `Bearer YOUR_TOKEN`
5. Try "GET /auth/me"

## **Database Access**

H2 Console: http://localhost:8081/api/h2-console
- JDBC URL: `jdbc:h2:mem:testdb`
- User: `sa`
- Password: `password`

That's it! Your backend is ready on port 8081! 🎉
