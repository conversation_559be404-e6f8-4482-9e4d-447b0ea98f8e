@echo off
echo 🐘 PostgreSQL Database Setup for Online Learning Platform
echo ========================================================

echo.
echo 📋 Database Configuration:
echo    Host: localhost
echo    Port: 5432
echo    Database: online_learning
echo    Username: postgres
echo    Password: password (default)

echo.
echo 🔧 Prerequisites:
echo    1. PostgreSQL must be installed and running
echo    2. PostgreSQL service should be started
echo    3. Default postgres user should be accessible

echo.
echo 🚀 Setting up database...

REM Check if PostgreSQL is running
echo 📡 Checking PostgreSQL connection...
psql -h localhost -p 5432 -U postgres -c "\l" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Error: Cannot connect to PostgreSQL!
    echo.
    echo 💡 Please ensure:
    echo    1. PostgreSQL is installed
    echo    2. PostgreSQL service is running
    echo    3. You can connect with: psql -U postgres
    echo.
    echo 📥 Download PostgreSQL from: https://www.postgresql.org/download/
    pause
    exit /b 1
)

echo ✅ PostgreSQL connection successful!

REM Create database
echo 📊 Creating database 'online_learning'...
psql -h localhost -p 5432 -U postgres -c "CREATE DATABASE online_learning;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Database 'online_learning' created successfully!
) else (
    echo ℹ️  Database 'online_learning' already exists or creation failed
    echo    This is normal if the database was already created
)

REM Verify database exists
echo 🔍 Verifying database exists...
psql -h localhost -p 5432 -U postgres -c "\l" | findstr "online_learning" >nul
if %errorlevel% equ 0 (
    echo ✅ Database 'online_learning' is ready!
) else (
    echo ❌ Database verification failed
    pause
    exit /b 1
)

echo.
echo 🎉 PostgreSQL setup completed successfully!
echo.
echo 📋 Connection Details:
echo    JDBC URL: ************************************************
echo    Username: postgres
echo    Password: password (or your postgres password)
echo.
echo 🚀 You can now start your Spring Boot application!
echo    Run: mvn clean install
echo    Then: mvn spring-boot:run
echo.
pause
