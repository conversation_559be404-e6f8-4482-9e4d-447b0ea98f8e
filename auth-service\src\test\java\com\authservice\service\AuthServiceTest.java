package com.authservice.service;

import com.authservice.dto.request.LoginRequest;
import com.authservice.dto.request.RegisterRequest;
import com.authservice.dto.response.AuthResponse;
import com.authservice.entity.RefreshToken;
import com.authservice.entity.User;
import com.authservice.enums.Role;
import com.authservice.exception.UserAlreadyExistsException;
import com.authservice.security.JwtTokenProvider;
import com.authservice.security.UserPrincipal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {
    
    @Mock
    private AuthenticationManager authenticationManager;
    
    @Mock
    private UserService userService;
    
    @Mock
    private RefreshTokenService refreshTokenService;
    
    @Mock
    private JwtTokenProvider jwtTokenProvider;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @InjectMocks
    private AuthService authService;
    
    private User testUser;
    private RefreshToken testRefreshToken;
    
    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(UUID.randomUUID());
        testUser.setFullName("John Doe");
        testUser.setEmail("<EMAIL>");
        testUser.setPasswordHash("hashed-password");
        testUser.setRole(Role.USER);
        testUser.setCreatedAt(LocalDateTime.now());
        testUser.setUpdatedAt(LocalDateTime.now());
        
        testRefreshToken = new RefreshToken();
        testRefreshToken.setToken("refresh-token");
        testRefreshToken.setUser(testUser);
        testRefreshToken.setExpiresAt(LocalDateTime.now().plusDays(7));
    }
    
    @Test
    void register_ValidRequest_ReturnsAuthResponse() {
        // Given
        RegisterRequest request = new RegisterRequest("John Doe", "<EMAIL>", "password123");
        
        when(userService.existsByEmail(request.getEmail())).thenReturn(false);
        when(passwordEncoder.encode(request.getPassword())).thenReturn("hashed-password");
        when(userService.save(any(User.class))).thenReturn(testUser);
        when(jwtTokenProvider.generateAccessToken(any(UUID.class), anyString(), anyString()))
            .thenReturn("access-token");
        when(jwtTokenProvider.getAccessTokenExpiration()).thenReturn(900000L);
        when(refreshTokenService.createRefreshToken(testUser)).thenReturn(testRefreshToken);
        
        // When
        AuthResponse response = authService.register(request);
        
        // Then
        assertNotNull(response);
        assertEquals("John Doe", response.getFullName());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals(Role.USER, response.getRole());
        assertEquals("access-token", response.getAccessToken());
        assertEquals("refresh-token", response.getRefreshToken());
        
        verify(userService).existsByEmail(request.getEmail());
        verify(userService).save(any(User.class));
        verify(refreshTokenService).createRefreshToken(testUser);
    }
    
    @Test
    void register_EmailAlreadyExists_ThrowsException() {
        // Given
        RegisterRequest request = new RegisterRequest("John Doe", "<EMAIL>", "password123");
        when(userService.existsByEmail(request.getEmail())).thenReturn(true);
        
        // When & Then
        assertThrows(UserAlreadyExistsException.class, () -> authService.register(request));
        verify(userService).existsByEmail(request.getEmail());
        verify(userService, never()).save(any(User.class));
    }
    
    @Test
    void login_ValidCredentials_ReturnsAuthResponse() {
        // Given
        LoginRequest request = new LoginRequest("<EMAIL>", "password123");
        UserPrincipal userPrincipal = UserPrincipal.create(testUser);
        Authentication authentication = mock(Authentication.class);
        
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
            .thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn(userPrincipal);
        when(jwtTokenProvider.generateAccessToken(authentication)).thenReturn("access-token");
        when(jwtTokenProvider.getAccessTokenExpiration()).thenReturn(900000L);
        when(userService.findById(userPrincipal.getId())).thenReturn(testUser);
        when(refreshTokenService.createRefreshToken(testUser)).thenReturn(testRefreshToken);
        
        // When
        AuthResponse response = authService.login(request);
        
        // Then
        assertNotNull(response);
        assertEquals("John Doe", response.getFullName());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals("access-token", response.getAccessToken());
        assertEquals("refresh-token", response.getRefreshToken());
        
        verify(authenticationManager).authenticate(any(UsernamePasswordAuthenticationToken.class));
        verify(refreshTokenService).createRefreshToken(testUser);
    }
}
