package com.authservice.service;

import com.authservice.dto.TokenPair;
import com.authservice.dto.request.LoginRequest;
import com.authservice.dto.request.RegisterRequest;
import com.authservice.dto.response.AuthResponse;
import com.authservice.dto.response.UserResponse;
import com.authservice.dto.response.UserStatsResponse;
import com.authservice.entity.RefreshToken;
import com.authservice.entity.User;
import com.authservice.enums.Role;
import com.authservice.exception.InvalidCredentialsException;
import com.authservice.exception.TokenExpiredException;
import com.authservice.exception.UserAlreadyExistsException;
import com.authservice.security.JwtTokenProvider;
import com.authservice.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@Service
public class AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RefreshTokenService refreshTokenService;
    
    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Transactional
    public AuthResponse register(RegisterRequest request) {
        logger.info("Registration attempt for email: {}", request.getEmail());

        if (userService.existsByEmail(request.getEmail())) {
            logger.warn("Registration failed - email already exists: {}", request.getEmail());
            throw new UserAlreadyExistsException("Email is already taken!");
        }

        // Create new user
        User user = new User();
        user.setFullName(request.getFullName());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setRole(Role.USER);

        User savedUser = userService.save(user);
        logger.info("New user registered successfully - ID: {}, Email: {}, Name: {}",
                   savedUser.getId(), savedUser.getEmail(), savedUser.getFullName());
        
        // Generate tokens
        String accessToken = jwtTokenProvider.generateAccessToken(
            savedUser.getId(), 
            savedUser.getEmail(), 
            savedUser.getRole().name()
        );
        
        RefreshToken refreshToken = refreshTokenService.createRefreshToken(savedUser);
        
        return new AuthResponse(
            savedUser.getId(),
            savedUser.getFullName(),
            savedUser.getEmail(),
            savedUser.getRole(),
            accessToken,
            refreshToken.getToken(),
            jwtTokenProvider.getAccessTokenExpiration()
        );
    }
    
    @Transactional
    public AuthResponse login(LoginRequest request) {
        logger.info("Login attempt for email: {}", request.getEmail());

        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        String accessToken = jwtTokenProvider.generateAccessToken(authentication);

        User user = userService.findById(userPrincipal.getId());
        RefreshToken refreshToken = refreshTokenService.createRefreshToken(user);

        logger.info("User logged in successfully - ID: {}, Email: {}",
                   userPrincipal.getId(), userPrincipal.getEmail());
        
        return new AuthResponse(
            userPrincipal.getId(),
            userPrincipal.getFullName(),
            userPrincipal.getEmail(),
            userPrincipal.getRole(),
            accessToken,
            refreshToken.getToken(),
            jwtTokenProvider.getAccessTokenExpiration()
        );
    }
    
    public TokenPair refreshToken(String refreshTokenStr) {
        return refreshTokenService.findByToken(refreshTokenStr)
            .map(refreshTokenService::verifyExpiration)
            .map(RefreshToken::getUser)
            .map(user -> {
                String accessToken = jwtTokenProvider.generateAccessToken(
                    user.getId(), 
                    user.getEmail(), 
                    user.getRole().name()
                );
                return new TokenPair(accessToken, refreshTokenStr);
            })
            .orElseThrow(() -> new TokenExpiredException("Refresh token is not in database!"));
    }
    
    public UserResponse getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        User user = userService.findById(userPrincipal.getId());

        return new UserResponse(
            user.getId(),
            user.getFullName(),
            user.getEmail(),
            user.getRole(),
            user.getCreatedAt(),
            user.getUpdatedAt()
        );
    }

    public UserStatsResponse getUserStats() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.truncatedTo(ChronoUnit.DAYS);
        LocalDateTime startOfWeek = now.minusDays(now.getDayOfWeek().getValue() - 1).truncatedTo(ChronoUnit.DAYS);
        LocalDateTime startOfMonth = now.withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);

        long totalUsers = userService.getTotalUserCount();
        long todaySignups = userService.getUsersCreatedAfter(startOfDay);
        long thisWeekSignups = userService.getUsersCreatedAfter(startOfWeek);
        long thisMonthSignups = userService.getUsersCreatedAfter(startOfMonth);

        User lastUser = userService.getLastRegisteredUser();
        LocalDateTime lastSignupTime = lastUser != null ? lastUser.getCreatedAt() : null;
        String lastSignupEmail = lastUser != null ? lastUser.getEmail() : null;

        logger.info("User stats requested - Total: {}, Today: {}, Week: {}, Month: {}",
                   totalUsers, todaySignups, thisWeekSignups, thisMonthSignups);

        return new UserStatsResponse(
            totalUsers,
            todaySignups,
            thisWeekSignups,
            thisMonthSignups,
            lastSignupTime,
            lastSignupEmail
        );
    }
}
