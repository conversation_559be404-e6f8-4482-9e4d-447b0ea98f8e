# PowerShell script to start Preply Authentication Service

Write-Host "Starting Preply Authentication Service..." -ForegroundColor Blue

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed. Please install Docker Desktop first." -ForegroundColor Red
    Write-Host "Download from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available. Please ensure Docker Desktop is running." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Start the services
Write-Host "Starting PostgreSQL and Auth Service..." -ForegroundColor Yellow
docker-compose up -d

# Wait for services to be ready
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check if services are running
$runningServices = docker-compose ps | Select-String "Up"
if ($runningServices) {
    Write-Host ""
    Write-Host "✅ Services started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Auth Service is running at: http://localhost:8080/api" -ForegroundColor Cyan
    Write-Host "📚 Swagger UI is available at: http://localhost:8080/api/swagger-ui.html" -ForegroundColor Cyan
    Write-Host "🐘 PostgreSQL is running at: localhost:5432" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📖 Setup Guide: See WINDOWS_SETUP_GUIDE.md" -ForegroundColor Yellow
    Write-Host "📖 API Documentation: See API_DOCUMENTATION.md" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🧪 Quick Test:" -ForegroundColor Magenta
    Write-Host "   1. Open: http://localhost:8080/api/swagger-ui.html" -ForegroundColor White
    Write-Host "   2. Try the /auth/register endpoint" -ForegroundColor White
    Write-Host "   3. Use the returned token for /auth/me endpoint" -ForegroundColor White
    Write-Host ""
    Write-Host "To stop the services, run: docker-compose down" -ForegroundColor Yellow
    
    # Optionally open Swagger UI in default browser
    $openBrowser = Read-Host "Would you like to open Swagger UI in your browser? (y/n)"
    if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
        Start-Process "http://localhost:8080/api/swagger-ui.html"
    }
} else {
    Write-Host "❌ Failed to start services. Check the logs with: docker-compose logs" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Read-Host "Press Enter to exit"
