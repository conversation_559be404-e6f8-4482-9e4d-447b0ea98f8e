#!/bin/bash

echo "Starting Preply Authentication Service..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Start the services
echo "Starting PostgreSQL and Auth Service..."
docker-compose up -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Services started successfully!"
    echo ""
    echo "🚀 Auth Service is running at: http://localhost:8080/api"
    echo "📚 Swagger UI is available at: http://localhost:8080/api/swagger-ui.html"
    echo "🐘 PostgreSQL is running at: localhost:5432"
    echo ""
    echo "📖 Setup Guide: See SETUP_AND_TESTING_GUIDE.md"
    echo "📖 API Documentation: See API_DOCUMENTATION.md"
    echo ""
    echo "🧪 Quick Test:"
    echo "   1. Open: http://localhost:8080/api/swagger-ui.html"
    echo "   2. Try the /auth/register endpoint"
    echo "   3. Use the returned token for /auth/me endpoint"
    echo ""
    echo "To stop the services, run: docker-compose down"
else
    echo "❌ Failed to start services. Check the logs with: docker-compose logs"
    exit 1
fi
