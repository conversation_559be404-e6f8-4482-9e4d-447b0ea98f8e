version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: online-learning-postgres
    environment:
      POSTGRES_DB: online_learning
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - online-learning-network

  auth-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: online-learning-auth-service
    environment:
      DB_USERNAME: postgres
      DB_PASSWORD: password
      JWT_SECRET: mySecretKey123456789012345678901234567890
      CORS_ORIGINS: http://localhost:3000,http://localhost:8081
    ports:
      - "8081:8081"
    depends_on:
      - postgres
    networks:
      - online-learning-network

volumes:
  postgres_data:

networks:
  online-learning-network:
    driver: bridge

networks:
  preply-network:
    driver: bridge
