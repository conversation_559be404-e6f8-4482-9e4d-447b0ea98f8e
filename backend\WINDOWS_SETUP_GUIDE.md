# 🪟 Windows Setup and Testing Guide

## Prerequisites for Windows

Before running the application, ensure you have the following installed:

- **Java 17+** (OpenJDK or Oracle JDK)
  - Download from: https://adoptium.net/
  - Verify: `java -version`
- **Maven 3.6+**
  - Download from: https://maven.apache.org/download.cgi
  - Verify: `mvn -version`
- **Docker Desktop for Windows**
  - Download from: https://www.docker.com/products/docker-desktop
  - Ensure it's running before proceeding

## 🐳 Option 1: Quick Start with Docker (Recommended)

### Step 1: Open Command Prompt or PowerShell
```cmd
# Navigate to the backend directory
cd backend
```

### Step 2: Start Services
```cmd
# Run the Windows batch script
start.bat
```

### Step 3: Verify Services
```cmd
# Check if services are running
docker-compose ps

# View logs
docker-compose logs auth-service
```

## 💻 Option 2: Manual Setup (Local Development)

### Step 1: Install PostgreSQL 16 for Windows
1. Download from: https://www.postgresql.org/download/windows/
2. Install with default settings
3. Remember the password you set for the `postgres` user

### Step 2: Create Database
```cmd
# Open Command Prompt as Administrator
# Navigate to PostgreSQL bin directory (usually C:\Program Files\PostgreSQL\16\bin)
cd "C:\Program Files\PostgreSQL\16\bin"

# Create database
createdb -U postgres preply_auth
```

### Step 3: Set Environment Variables
**Option A: Command Prompt**
```cmd
set DB_USERNAME=postgres
set DB_PASSWORD=your_password
set JWT_SECRET=mySecretKey123456789012345678901234567890
```

**Option B: PowerShell**
```powershell
$env:DB_USERNAME="postgres"
$env:DB_PASSWORD="your_password"
$env:JWT_SECRET="mySecretKey123456789012345678901234567890"
```

**Option C: System Environment Variables**
1. Press `Win + R`, type `sysdm.cpl`, press Enter
2. Click "Environment Variables"
3. Add the variables under "User variables"

### Step 4: Build and Run
```cmd
# Build the application
mvn clean package -DskipTests

# Run the application
mvn spring-boot:run

# Or run the JAR file
java -jar target\auth-service-1.0.0.jar
```

## 🧪 Testing the API on Windows

### 1. Access Swagger UI
Open your browser and navigate to:
```
http://localhost:8080/api/swagger-ui.html
```

### 2. Automated Testing
```cmd
# Run the Windows test script
test-api.bat
```

### 3. Manual Testing with PowerShell

#### Register a User
```powershell
$headers = @{ "Content-Type" = "application/json" }
$body = @{
    full_name = "John Doe"
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

$response = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/register" -Method Post -Headers $headers -Body $body
$response
```

#### Login
```powershell
$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
} | ConvertTo-Json

$loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/login" -Method Post -Headers $headers -Body $loginBody
$accessToken = $loginResponse.access_token
```

#### Get Current User
```powershell
$authHeaders = @{ 
    "Authorization" = "Bearer $accessToken"
    "Content-Type" = "application/json"
}

$userResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/me" -Method Get -Headers $authHeaders
$userResponse
```

### 4. Testing with curl (if installed)
If you have curl installed on Windows:

```cmd
REM Register User
curl -X POST "http://localhost:8080/api/auth/register" ^
  -H "Content-Type: application/json" ^
  -d "{\"full_name\": \"Jane Doe\", \"email\": \"<EMAIL>\", \"password\": \"password123\"}"

REM Login
curl -X POST "http://localhost:8080/api/auth/login" ^
  -H "Content-Type: application/json" ^
  -d "{\"email\": \"<EMAIL>\", \"password\": \"password123\"}"
```

## 🧪 Running Unit Tests

```cmd
# Run all tests
mvn test

# Run tests with coverage
mvn test jacoco:report

# Run specific test class
mvn test -Dtest=AuthControllerTest
```

## 🔧 Windows-Specific Troubleshooting

### 1. Port Issues
```cmd
# Check what's using port 8080
netstat -ano | findstr :8080

# Kill process if needed (replace PID with actual process ID)
taskkill /PID <PID> /F
```

### 2. Docker Issues
```cmd
# Restart Docker Desktop
# Check Docker is running
docker version

# If Docker Compose issues, try:
docker-compose down
docker-compose up -d --force-recreate
```

### 3. Java Issues
```cmd
# Check Java version
java -version

# Check JAVA_HOME
echo %JAVA_HOME%

# If not set, add to environment variables:
# JAVA_HOME = C:\Program Files\Java\jdk-17
```

### 4. Maven Issues
```cmd
# Check Maven version
mvn -version

# If Maven not found, add to PATH:
# Add C:\apache-maven-3.x.x\bin to PATH environment variable
```

### 5. PostgreSQL Connection Issues
```cmd
# Test PostgreSQL connection
psql -U postgres -d preply_auth -h localhost -p 5432

# If connection fails, check:
# 1. PostgreSQL service is running (services.msc)
# 2. Firewall settings
# 3. pg_hba.conf configuration
```

## 🚀 Quick Commands Summary

```cmd
# Start everything with Docker
start.bat

# Test the API
test-api.bat

# Stop Docker services
docker-compose down

# View logs
docker-compose logs -f auth-service

# Rebuild and restart
docker-compose down
docker-compose up -d --build
```

## 📊 Useful Windows Tools

1. **Postman**: GUI for API testing
   - Download: https://www.postman.com/downloads/
   - Import the Swagger JSON for easy testing

2. **Windows Terminal**: Better command line experience
   - Install from Microsoft Store

3. **Git Bash**: Unix-like commands on Windows
   - Comes with Git for Windows
   - Allows using the original .sh scripts

## 🎯 Next Steps

1. **Open Swagger UI**: http://localhost:8080/api/swagger-ui.html
2. **Test the endpoints** using the interactive interface
3. **Integrate with your frontend** application
4. **Deploy to production** with proper environment variables

Happy coding on Windows! 🎉
