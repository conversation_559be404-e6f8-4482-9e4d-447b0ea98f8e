import { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { 
  CreditCard, 
  Smartphone, 
  Star, 
  ChevronLeft, 
  ChevronRight,
  Info,
  Shield,
  Trash2,
  Calendar
} from "lucide-react";
import { format } from "date-fns";

const Payment = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const bookingDetails = location.state?.bookingDetails;

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("card");
  const [cardNumber, setCardNumber] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvc, setCvc] = useState("");
  const [saveCard, setSaveCard] = useState(false);
  const [promoCode, setPromoCode] = useState("");

  if (!bookingDetails) {
    navigate("/find-tutor");
    return null;
  }

  const { tutor, duration, date, time } = bookingDetails;
  const lessonPrice = duration === 25 ? 1829 : 3032;
  const processingFee = 25.76;
  const total = lessonPrice + processingFee;

  const handlePayment = () => {
    // Handle payment processing
    console.log("Processing payment...");
  };

  const paymentMethods = [
    { id: "card", label: "Card", icon: CreditCard },
    { id: "apple-pay", label: "Apple Pay", icon: Smartphone },
    { id: "google-pay", label: "Google Pay", icon: Smartphone },
    { id: "paypal", label: "PayPal", icon: CreditCard },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Booking Summary */}
          <div className="space-y-6">
            {/* Confidence Banner */}
            <div className="bg-green-100 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-800 mb-2">Book with confidence!</h3>
              <p className="text-sm text-green-700">
                If {tutor.name} isn't a match, get 2 more free trials to find the right tutor.
              </p>
            </div>

            {/* Tutor Info */}
            <Card className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <img
                  src={tutor.avatar}
                  alt={tutor.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900">
                    English with {tutor.name}
                  </h3>
                  <p className="text-gray-600">
                    {format(date, "EEEE, MMM d")}, {time} – {
                      duration === 25 ? "14:25" : "14:50"
                    }
                  </p>
                </div>
                <div className="flex gap-2">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <Calendar className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>

            {/* Order Summary */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Your order</h3>
              
              <div className="flex gap-4 mb-4">
                <Badge variant="outline" className="text-sm">
                  {duration} mins • ₹{duration === 25 ? "1,829" : "3,032"}
                </Badge>
              </div>

              <div className="space-y-3 border-t pt-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">{duration}-min lesson</span>
                  <span className="font-medium">₹{lessonPrice.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-1">
                    <span className="text-gray-600">Processing fee</span>
                    <Info className="h-4 w-4 text-gray-400" />
                  </div>
                  <span className="font-medium">₹{processingFee}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold border-t pt-3">
                  <span>Total</span>
                  <div className="text-right">
                    <div>₹{total.toLocaleString()}</div>
                    <div className="text-sm text-gray-500 font-normal">
                      charged as $35.30
                    </div>
                  </div>
                </div>
              </div>

              <Button variant="link" className="text-sm text-gray-600 p-0 h-auto mt-4">
                Have a promo code?
              </Button>
            </Card>

            {/* Free Cancellation */}
            <div className="flex items-start gap-3 p-4 bg-white rounded-lg border">
              <Shield className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-900">Free cancellation</h4>
                <p className="text-sm text-gray-600">
                  Cancel or reschedule for free until 02:00 on {format(date, "EEE, MMM d")}.
                </p>
              </div>
            </div>
          </div>

          {/* Right Column - Payment */}
          <div className="space-y-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Payment method</h3>

              {/* Payment Method Selection */}
              <div className="grid grid-cols-2 gap-3 mb-6">
                {paymentMethods.map((method) => {
                  const Icon = method.icon;
                  return (
                    <Button
                      key={method.id}
                      variant={selectedPaymentMethod === method.id ? "default" : "outline"}
                      onClick={() => setSelectedPaymentMethod(method.id)}
                      className="h-12 justify-center"
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      {method.label}
                    </Button>
                  );
                })}
              </div>

              {/* Card Form */}
              {selectedPaymentMethod === "card" && (
                <div className="space-y-4">
                  <div>
                    <Input
                      placeholder="1234 1234 1234 1234"
                      value={cardNumber}
                      onChange={(e) => setCardNumber(e.target.value)}
                      className="text-base"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <Input
                      placeholder="MM/YY"
                      value={expiryDate}
                      onChange={(e) => setExpiryDate(e.target.value)}
                    />
                    <Input
                      placeholder="CVC"
                      value={cvc}
                      onChange={(e) => setCvc(e.target.value)}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="save-card"
                      checked={saveCard}
                      onCheckedChange={setSaveCard}
                    />
                    <label htmlFor="save-card" className="text-sm text-gray-700">
                      Save this card for future payments
                    </label>
                  </div>
                </div>
              )}

              <Button
                onClick={handlePayment}
                className="w-full mt-6 bg-gray-300 hover:bg-gray-400 text-gray-700 h-12 text-base"
              >
                Confirm payment • ₹{total.toLocaleString()}
              </Button>

              <div className="mt-4 text-xs text-gray-500 text-center">
                By pressing the "Confirm payment • ₹{total.toLocaleString()}" button, you agree to{" "}
                <a href="#" className="underline">Preply's Refund and Payment Policy</a>
              </div>

              <div className="mt-4 text-xs text-gray-500 text-center">
                It's safe to pay on Preply. All transactions are protected by SSL encryption.
              </div>
            </Card>

            {/* Reviews Section */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-400 fill-current" />
                  <span className="font-semibold">5</span>
                  <span className="text-gray-600">17 reviews</span>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium">M</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Maximiliano</h4>
                    <p className="text-sm text-gray-600 mt-1">
                      {tutor.name} is very professional, we started all lessons at o'clock. She is
                      very talkative and easy-going, provides positive feedback, and
                      corrected me when I'm wrong on something. She also provided me
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Payment;
