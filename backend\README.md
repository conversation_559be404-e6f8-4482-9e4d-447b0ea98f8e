# Authentication Microservice

A production-ready authentication microservice built with Spring Boot 3 and PostgreSQL.

## Features

- User registration and login
- JWT-based authentication (access + refresh tokens)
- Spring Security with BCrypt password hashing
- Input validation with structured error responses
- PostgreSQL database integration
- Comprehensive unit tests

## Tech Stack

- Java 17+
- Spring Boot 3
- Spring Security
- Spring Data JPA
- PostgreSQL 16
- JWT (JSON Web Tokens)
- Maven
- JUnit 5

## API Endpoints

- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user (secured)
- `POST /auth/refresh-token` - Refresh access token

## Database Schema

Users table with fields:
- id (UUID)
- full_name
- email
- password_hash
- role
- created_at
- updated_at

## Quick Start

### Option 1: Docker (Recommended)
```bash
chmod +x start.sh
./start.sh
```

### Option 2: Manual Setup
1. Ensure PostgreSQL 16 is running
2. Update database configuration in `application.properties`
3. Run `mvn spring-boot:run`

## API Documentation

- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **API Docs**: http://localhost:8080/api/v3/api-docs

## Testing

### Automated Testing
```bash
# Run unit tests
mvn test

# Test API endpoints
chmod +x test-api.sh
./test-api.sh
```

### Manual Testing
1. Open Swagger UI: http://localhost:8080/api/swagger-ui.html
2. Try the `/auth/register` endpoint
3. Use the returned token for protected endpoints

For detailed testing guide, see: `SETUP_AND_TESTING_GUIDE.md`
