package com.authservice.dto.response;

import java.time.LocalDateTime;

public class UserStatsResponse {
    private long totalUsers;
    private long todaySignups;
    private long thisWeekSignups;
    private long thisMonthSignups;
    private LocalDateTime lastSignupTime;
    private String lastSignupEmail;
    
    // Constructors
    public UserStatsResponse() {}
    
    public UserStatsResponse(long totalUsers, long todaySignups, long thisWeekSignups, 
                           long thisMonthSignups, LocalDateTime lastSignupTime, String lastSignupEmail) {
        this.totalUsers = totalUsers;
        this.todaySignups = todaySignups;
        this.thisWeekSignups = thisWeekSignups;
        this.thisMonthSignups = thisMonthSignups;
        this.lastSignupTime = lastSignupTime;
        this.lastSignupEmail = lastSignupEmail;
    }
    
    // Getters and Setters
    public long getTotalUsers() {
        return totalUsers;
    }
    
    public void setTotalUsers(long totalUsers) {
        this.totalUsers = totalUsers;
    }
    
    public long getTodaySignups() {
        return todaySignups;
    }
    
    public void setTodaySignups(long todaySignups) {
        this.todaySignups = todaySignups;
    }
    
    public long getThisWeekSignups() {
        return thisWeekSignups;
    }
    
    public void setThisWeekSignups(long thisWeekSignups) {
        this.thisWeekSignups = thisWeekSignups;
    }
    
    public long getThisMonthSignups() {
        return thisMonthSignups;
    }
    
    public void setThisMonthSignups(long thisMonthSignups) {
        this.thisMonthSignups = thisMonthSignups;
    }
    
    public LocalDateTime getLastSignupTime() {
        return lastSignupTime;
    }
    
    public void setLastSignupTime(LocalDateTime lastSignupTime) {
        this.lastSignupTime = lastSignupTime;
    }
    
    public String getLastSignupEmail() {
        return lastSignupEmail;
    }
    
    public void setLastSignupEmail(String lastSignupEmail) {
        this.lastSignupEmail = lastSignupEmail;
    }
}
