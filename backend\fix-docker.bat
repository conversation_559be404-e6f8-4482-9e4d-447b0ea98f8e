@echo off
echo 🔧 Fixing Docker build issues...

echo.
echo Step 1: Cleaning up Docker
docker-compose down -v
docker system prune -f

echo.
echo Step 2: Pulling required images manually
docker pull eclipse-temurin:17-jdk
docker pull eclipse-temurin:17-jre-jammy
docker pull postgres:16

echo.
echo Step 3: Using alternative Dockerfile
copy Dockerfile.alternative Dockerfile.backup
copy Dockerfile.alternative Dockerfile

echo.
echo Step 4: Building with no cache
docker-compose build --no-cache

echo.
echo Step 5: Starting services
docker-compose up -d

echo.
echo ✅ Docker fix complete! Services should be starting...
timeout /t 10 /nobreak >nul

docker-compose ps

pause
