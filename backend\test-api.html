<!DOCTYPE html>
<html>
<head>
    <title>Auth API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Auth API Test Page</h1>
        <p>Base URL: <strong>http://localhost:8080/api</strong></p>
        
        <div class="test-section">
            <h3>1. Register User</h3>
            <button onclick="testRegister()">Test Registration</button>
            <div id="registerResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Login User</h3>
            <button onclick="testLogin()">Test Login</button>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Get Current User (Protected)</h3>
            <button onclick="testMe()">Test /auth/me</button>
            <div id="meResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Refresh Token</h3>
            <button onclick="testRefresh()">Test Token Refresh</button>
            <div id="refreshResult" class="result"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080/api';
        let accessToken = '';
        let refreshToken = '';

        async function testRegister() {
            const result = document.getElementById('registerResult');
            try {
                const response = await fetch(`${BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        full_name: 'Test User',
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access_token;
                    refreshToken = data.refresh_token;
                    result.className = 'result success';
                    result.textContent = `✅ Registration successful!\nAccess Token: ${accessToken.substring(0, 50)}...\nUser: ${data.user.full_name} (${data.user.email})`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ Registration failed: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Error: ${error.message}`;
            }
        }

        async function testLogin() {
            const result = document.getElementById('loginResult');
            try {
                const response = await fetch(`${BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access_token;
                    refreshToken = data.refresh_token;
                    result.className = 'result success';
                    result.textContent = `✅ Login successful!\nAccess Token: ${accessToken.substring(0, 50)}...\nUser: ${data.user.full_name} (${data.user.email})`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ Login failed: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Error: ${error.message}`;
            }
        }

        async function testMe() {
            const result = document.getElementById('meResult');
            if (!accessToken) {
                result.className = 'result error';
                result.textContent = '❌ No access token. Please register or login first.';
                return;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/auth/me`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    result.className = 'result success';
                    result.textContent = `✅ Current user retrieved!\n${JSON.stringify(data, null, 2)}`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ Failed to get current user: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Error: ${error.message}`;
            }
        }

        async function testRefresh() {
            const result = document.getElementById('refreshResult');
            if (!refreshToken) {
                result.className = 'result error';
                result.textContent = '❌ No refresh token. Please register or login first.';
                return;
            }
            
            try {
                const response = await fetch(`${BASE_URL}/auth/refresh-token`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        refresh_token: refreshToken
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    accessToken = data.access_token;
                    refreshToken = data.refresh_token;
                    result.className = 'result success';
                    result.textContent = `✅ Token refreshed!\nNew Access Token: ${accessToken.substring(0, 50)}...`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ Token refresh failed: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
