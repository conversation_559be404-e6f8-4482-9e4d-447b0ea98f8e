import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { X } from "lucide-react";

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (feedback: { reason: string; details?: string }) => void;
}

const FeedbackModal = ({ isOpen, onClose, onSubmit }: FeedbackModalProps) => {
  const [selectedReason, setSelectedReason] = useState<string>("");
  const [otherDetails, setOtherDetails] = useState<string>("");

  const feedbackOptions = [
    "I need more information about the platform",
    "I need more information about the tutor",
    "I need to change the lesson date or time",
    "I expected the trial lesson to be free",
    "I'm experiencing a payment error",
    "Other"
  ];

  const handleSubmit = () => {
    if (selectedReason) {
      onSubmit({
        reason: selectedReason,
        details: selectedReason === "Other" ? otherDetails : undefined
      });
      // Reset form
      setSelectedReason("");
      setOtherDetails("");
      onClose();
    }
  };

  const handleClose = () => {
    setSelectedReason("");
    setOtherDetails("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md p-0 gap-0">
        <DialogHeader className="p-6 pb-4">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold text-gray-900">
              Why are you leaving?
            </DialogTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-6 w-6 rounded-full"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Help us improve by sharing your feedback
          </p>
        </DialogHeader>

        <div className="px-6 pb-6 space-y-3">
          {feedbackOptions.map((option) => (
            <div
              key={option}
              className={`border rounded-lg p-4 cursor-pointer transition-all ${
                selectedReason === option
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:border-gray-300"
              }`}
              onClick={() => setSelectedReason(option)}
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">{option}</span>
                <div
                  className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                    selectedReason === option
                      ? "border-blue-500 bg-blue-500"
                      : "border-gray-300"
                  }`}
                >
                  {selectedReason === option && (
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  )}
                </div>
              </div>
            </div>
          ))}

          {/* Other details textarea */}
          {selectedReason === "Other" && (
            <div className="mt-4">
              <Textarea
                placeholder="Please tell us more..."
                value={otherDetails}
                onChange={(e) => setOtherDetails(e.target.value)}
                className="min-h-[100px] border-blue-300 focus:border-blue-500"
              />
            </div>
          )}

          <Button
            onClick={handleSubmit}
            disabled={!selectedReason || (selectedReason === "Other" && !otherDetails.trim())}
            className="w-full mt-6 bg-gray-300 hover:bg-gray-400 text-gray-700 disabled:bg-gray-200 disabled:text-gray-400"
          >
            Send feedback
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FeedbackModal;
