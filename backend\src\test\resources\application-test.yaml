spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: password
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect

jwt:
  secret: testSecretKey123456789012345678901234567890
  access-token-expiration: 900000
  refresh-token-expiration: 604800000

logging:
  level:
    com.preply: DEBUG
