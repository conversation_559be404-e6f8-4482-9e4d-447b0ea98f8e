@echo off
echo 🚀 Quick Start - Preply Auth Service with H2 Database
echo ===================================================

REM Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed or not in PATH
    echo Please install Java 17+ from: https://adoptium.net/
    echo.
    pause
    exit /b 1
)

echo ✅ Java is available

REM Check if <PERSON><PERSON> is installed
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven is not installed or not in PATH
    echo Please install Maven from: https://maven.apache.org/download.cgi
    echo.
    echo Alternative: Use Maven Wrapper
    echo   .\mvnw.cmd spring-boot:run
    echo.
    pause
    exit /b 1
)

echo ✅ Maven is available

echo.
echo 📋 Configuration:
echo    Database: H2 In-Memory (no setup required)
echo    Port: 8081
echo    Context Path: /api
echo.

echo 🔨 Building application...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.
echo 🚀 Starting application...
echo.
echo 📚 Once started, you can access:
echo    • Swagger UI: http://localhost:8081/api/swagger-ui.html
echo    • H2 Console: http://localhost:8081/api/h2-console
echo    • API Base: http://localhost:8081/api
echo.
echo 💡 H2 Console Login:
echo    JDBC URL: jdbc:h2:mem:testdb
echo    User: sa
echo    Password: password
echo.
echo Press Ctrl+C to stop the application
echo.

REM Start the application
java -jar target\auth-service-1.0.0.jar

pause
