@echo off
echo 🐘 Starting PostgreSQL Database for Online Learning Platform
echo ============================================================

echo.
echo 🚀 Starting PostgreSQL container...
docker-compose up postgres -d

echo.
echo ⏳ Waiting for PostgreSQL to be ready...
timeout /t 5 /nobreak >nul

echo.
echo 🔍 Checking PostgreSQL status...
docker ps | findstr "online-learning-postgres"

if %errorlevel% equ 0 (
    echo ✅ PostgreSQL is running successfully!
    echo.
    echo 📋 Connection Details:
    echo    Host: localhost
    echo    Port: 5432
    echo    Database: online_learning
    echo    Username: postgres
    echo    Password: password
    echo.
    echo 🚀 You can now start your Spring Boot application:
    echo    mvn clean install
    echo    mvn spring-boot:run
) else (
    echo ❌ Failed to start PostgreSQL container
    echo.
    echo 💡 Make sure Dock<PERSON> is running and try again
)

echo.
pause
