#!/bin/bash

# Configuration
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-5432}
DB_NAME=${DB_NAME:-authdb}
DB_USER=${DB_USERNAME:-postgres}

echo "🔍 Checking user signups in authentication service..."
echo "=================================================="

# Check if PostgreSQL is available
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL client (psql) is not installed"
    exit 1
fi

# Function to execute SQL query
execute_query() {
    local query="$1"
    local description="$2"
    
    echo "📊 $description"
    echo "---"
    
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "$query" 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to execute query. Check your database connection."
        return 1
    fi
    
    echo ""
}

# Total users
execute_query "SELECT COUNT(*) as total_users FROM users;" "Total Users"

# Recent signups (last 24 hours)
execute_query "
SELECT COUNT(*) as signups_today 
FROM users 
WHERE created_at >= CURRENT_DATE;
" "Signups Today"

# Recent signups (last 7 days)
execute_query "
SELECT COUNT(*) as signups_this_week 
FROM users 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days';
" "Signups This Week"

# Recent signups (last 30 days)
execute_query "
SELECT COUNT(*) as signups_this_month 
FROM users 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days';
" "Signups This Month"

# Latest 5 signups
execute_query "
SELECT 
    full_name,
    email,
    role,
    created_at
FROM users 
ORDER BY created_at DESC 
LIMIT 5;
" "Latest 5 Signups"

# Signups by day (last 7 days)
execute_query "
SELECT 
    DATE(created_at) as signup_date,
    COUNT(*) as signups
FROM users 
WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY signup_date DESC;
" "Daily Signups (Last 7 Days)"

# User roles distribution
execute_query "
SELECT 
    role,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM users), 2) as percentage
FROM users 
GROUP BY role
ORDER BY count DESC;
" "User Roles Distribution"

echo "✅ Signup check completed!"
echo ""
echo "💡 To run this script:"
echo "   chmod +x scripts/check-signups.sh"
echo "   ./scripts/check-signups.sh"
echo ""
echo "🔧 To set custom database connection:"
echo "   DB_HOST=your-host DB_NAME=your-db ./scripts/check-signups.sh"
