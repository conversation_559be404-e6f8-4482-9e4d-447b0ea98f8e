package com.authservice.controller;

import com.authservice.dto.TokenPair;
import com.authservice.dto.request.LoginRequest;
import com.authservice.dto.request.RefreshTokenRequest;
import com.authservice.dto.request.RegisterRequest;
import com.authservice.dto.response.AuthResponse;
import com.authservice.dto.response.UserResponse;
import com.authservice.enums.Role;
import com.authservice.service.AuthService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AuthController.class)
class AuthControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private AuthService authService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void register_ValidRequest_ReturnsAuthResponse() throws Exception {
        // Given
        RegisterRequest request = new RegisterRequest("John Doe", "<EMAIL>", "password123");
        AuthResponse response = new AuthResponse(
            UUID.randomUUID(),
            "John Doe",
            "<EMAIL>",
            Role.USER,
            "access-token",
            "refresh-token",
            900000L
        );
        
        when(authService.register(any(RegisterRequest.class))).thenReturn(response);
        
        // When & Then
        mockMvc.perform(post("/auth/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.fullName").value("John Doe"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.role").value("USER"))
                .andExpect(jsonPath("$.accessToken").value("access-token"))
                .andExpect(jsonPath("$.refreshToken").value("refresh-token"));
    }
    
    @Test
    void register_InvalidRequest_ReturnsBadRequest() throws Exception {
        // Given
        RegisterRequest request = new RegisterRequest("", "invalid-email", "123"); // Invalid data
        
        // When & Then
        mockMvc.perform(post("/auth/register")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    void login_ValidRequest_ReturnsAuthResponse() throws Exception {
        // Given
        LoginRequest request = new LoginRequest("<EMAIL>", "password123");
        AuthResponse response = new AuthResponse(
            UUID.randomUUID(),
            "John Doe",
            "<EMAIL>",
            Role.USER,
            "access-token",
            "refresh-token",
            900000L
        );
        
        when(authService.login(any(LoginRequest.class))).thenReturn(response);
        
        // When & Then
        mockMvc.perform(post("/auth/login")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.accessToken").value("access-token"));
    }
    
    @Test
    @WithMockUser
    void getCurrentUser_AuthenticatedUser_ReturnsUserResponse() throws Exception {
        // Given
        UserResponse response = new UserResponse(
            UUID.randomUUID(),
            "John Doe",
            "<EMAIL>",
            Role.USER,
            LocalDateTime.now(),
            LocalDateTime.now()
        );
        
        when(authService.getCurrentUser()).thenReturn(response);
        
        // When & Then
        mockMvc.perform(get("/auth/me"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.fullName").value("John Doe"))
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }
    
    @Test
    void refreshToken_ValidRequest_ReturnsTokenPair() throws Exception {
        // Given
        RefreshTokenRequest request = new RefreshTokenRequest("refresh-token");
        TokenPair response = new TokenPair("new-access-token", "refresh-token");
        
        when(authService.refreshToken(anyString())).thenReturn(response);
        
        // When & Then
        mockMvc.perform(post("/auth/refresh-token")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("new-access-token"))
                .andExpect(jsonPath("$.refreshToken").value("refresh-token"));
    }
}
