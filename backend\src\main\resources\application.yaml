server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: auth-service
  
  datasource:
    url: ********************************************
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: NON_NULL

logging:
  level:
    com.preply: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

jwt:
  secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890}
  access-token-expiration: 900000  # 15 minutes
  refresh-token-expiration: 604800000  # 7 days

cors:
  allowed-origins: ${CORS_ORIGINS:http://localhost:3000,http://localhost:8081}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
