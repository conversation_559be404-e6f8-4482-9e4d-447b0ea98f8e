#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:8080/api"

echo -e "${BLUE}🧪 Testing Preply Authentication API${NC}"
echo "=================================="

# Check if service is running
echo -e "\n${YELLOW}1. Checking if service is running...${NC}"
if curl -s "$BASE_URL/swagger-ui.html" > /dev/null; then
    echo -e "${GREEN}✅ Service is running${NC}"
else
    echo -e "${RED}❌ Service is not running. Please start it first with: ./start.sh${NC}"
    exit 1
fi

# Test 1: Register a new user
echo -e "\n${YELLOW}2. Testing user registration...${NC}"
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "Test User",
    "email": "<EMAIL>",
    "password": "password123"
  }')

if echo "$REGISTER_RESPONSE" | grep -q "access_token"; then
    echo -e "${GREEN}✅ User registration successful${NC}"
    ACCESS_TOKEN=$(echo "$REGISTER_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    REFRESH_TOKEN=$(echo "$REGISTER_RESPONSE" | grep -o '"refresh_token":"[^"]*"' | cut -d'"' -f4)
    echo "Access Token: ${ACCESS_TOKEN:0:50}..."
else
    echo -e "${RED}❌ User registration failed${NC}"
    echo "Response: $REGISTER_RESPONSE"
fi

# Test 2: Login with the same user
echo -e "\n${YELLOW}3. Testing user login...${NC}"
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

if echo "$LOGIN_RESPONSE" | grep -q "access_token"; then
    echo -e "${GREEN}✅ User login successful${NC}"
    LOGIN_ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo "Login Access Token: ${LOGIN_ACCESS_TOKEN:0:50}..."
else
    echo -e "${RED}❌ User login failed${NC}"
    echo "Response: $LOGIN_RESPONSE"
fi

# Test 3: Get current user (protected endpoint)
echo -e "\n${YELLOW}4. Testing protected endpoint (/auth/me)...${NC}"
if [ ! -z "$ACCESS_TOKEN" ]; then
    ME_RESPONSE=$(curl -s -X GET "$BASE_URL/auth/me" \
      -H "Authorization: Bearer $ACCESS_TOKEN")
    
    if echo "$ME_RESPONSE" | grep -q "<EMAIL>"; then
        echo -e "${GREEN}✅ Protected endpoint access successful${NC}"
        echo "User: $(echo "$ME_RESPONSE" | grep -o '"full_name":"[^"]*"' | cut -d'"' -f4)"
    else
        echo -e "${RED}❌ Protected endpoint access failed${NC}"
        echo "Response: $ME_RESPONSE"
    fi
else
    echo -e "${RED}❌ Skipping protected endpoint test (no access token)${NC}"
fi

# Test 4: Refresh token
echo -e "\n${YELLOW}5. Testing token refresh...${NC}"
if [ ! -z "$REFRESH_TOKEN" ]; then
    REFRESH_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/refresh-token" \
      -H "Content-Type: application/json" \
      -d "{\"refresh_token\": \"$REFRESH_TOKEN\"}")
    
    if echo "$REFRESH_RESPONSE" | grep -q "access_token"; then
        echo -e "${GREEN}✅ Token refresh successful${NC}"
        NEW_ACCESS_TOKEN=$(echo "$REFRESH_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        echo "New Access Token: ${NEW_ACCESS_TOKEN:0:50}..."
    else
        echo -e "${RED}❌ Token refresh failed${NC}"
        echo "Response: $REFRESH_RESPONSE"
    fi
else
    echo -e "${RED}❌ Skipping token refresh test (no refresh token)${NC}"
fi

# Test 5: Invalid login
echo -e "\n${YELLOW}6. Testing invalid login...${NC}"
INVALID_LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword"
  }')

if echo "$INVALID_LOGIN_RESPONSE" | grep -q "Invalid email or password"; then
    echo -e "${GREEN}✅ Invalid login properly rejected${NC}"
else
    echo -e "${RED}❌ Invalid login test failed${NC}"
    echo "Response: $INVALID_LOGIN_RESPONSE"
fi

# Test 6: Duplicate registration
echo -e "\n${YELLOW}7. Testing duplicate registration...${NC}"
DUPLICATE_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "full_name": "Test User 2",
    "email": "<EMAIL>",
    "password": "password123"
  }')

if echo "$DUPLICATE_RESPONSE" | grep -q "Email already registered"; then
    echo -e "${GREEN}✅ Duplicate registration properly rejected${NC}"
else
    echo -e "${RED}❌ Duplicate registration test failed${NC}"
    echo "Response: $DUPLICATE_RESPONSE"
fi

echo -e "\n${BLUE}🎉 API Testing Complete!${NC}"
echo "=================================="
echo -e "${YELLOW}📚 Open Swagger UI for interactive testing:${NC}"
echo "   http://localhost:8080/api/swagger-ui.html"
echo ""
echo -e "${YELLOW}📖 For more testing options, see:${NC}"
echo "   SETUP_AND_TESTING_GUIDE.md"
