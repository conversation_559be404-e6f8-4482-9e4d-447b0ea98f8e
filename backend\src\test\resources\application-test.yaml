# Test Database Configuration
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=password
spring.datasource.driver-class-name=org.h2.Driver

# Test JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect

# Test JWT Configuration
jwt.secret=testSecretKey123456789012345678901234567890
jwt.access-token-expiration=900000
jwt.refresh-token-expiration=604800000

# Test Logging Configuration
logging.level.com.preply=DEBUG
