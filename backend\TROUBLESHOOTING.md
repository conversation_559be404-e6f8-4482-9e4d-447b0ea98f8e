# 🔧 Troubleshooting Guide

## Docker Issues

### Issue: Maven image not found
```
failed to solve: docker.io/library/maven:3.9.4-openjdk-17-slim: not found
```

**Solutions:**

#### Option 1: Use the fix script
```cmd
fix-docker.bat
```

#### Option 2: Manual fix
```cmd
# Clean up Docker
docker-compose down -v
docker system prune -f

# Pull images manually
docker pull eclipse-temurin:17-jdk
docker pull postgres:16

# Use alternative Dockerfile
copy Dockerfile.alternative Dockerfile

# Build with no cache
docker-compose build --no-cache
docker-compose up -d
```

#### Option 3: Skip Docker entirely
```cmd
# Use manual setup instead
setup-postgres.bat
start-manual.bat
```

### Issue: Docker Desktop not running
```
error during connect: Get "http://%2F%2F.%2Fpipe%2Fdocker_engine/v1.24/version"
```

**Solution:**
1. Start Docker Desktop
2. Wait for it to fully load
3. Try again

### Issue: Port 5432 already in use
```
bind: address already in use
```

**Solutions:**
```cmd
# Check what's using the port
netstat -ano | findstr :5432

# Stop existing PostgreSQL service
net stop postgresql-x64-16

# Or change port in docker-compose.yml
# Change "5432:5432" to "5433:5432"
```

## Manual Setup Issues

### Issue: Java not found
```
'java' is not recognized as an internal or external command
```

**Solution:**
1. Install Java 17+ from https://adoptium.net/
2. Add to PATH: `C:\Program Files\Eclipse Adoptium\jdk-17.x.x-hotspot\bin`
3. Verify: `java -version`

### Issue: Maven not found
```
'mvn' is not recognized as an internal or external command
```

**Solution:**
1. Download Maven from https://maven.apache.org/download.cgi
2. Extract to `C:\apache-maven-3.x.x`
3. Add to PATH: `C:\apache-maven-3.x.x\bin`
4. Set MAVEN_HOME: `C:\apache-maven-3.x.x`
5. Verify: `mvn -version`

### Issue: PostgreSQL connection failed
```
Connection to database failed
```

**Solutions:**

#### Check PostgreSQL service
```cmd
# Check if service is running
sc query postgresql-x64-16

# Start service if stopped
net start postgresql-x64-16
```

#### Check connection manually
```cmd
# Test connection
psql -U postgres -h localhost -p 5432

# If password prompt appears, PostgreSQL is running
```

#### Reset PostgreSQL password
1. Find `pg_hba.conf` file (usually in `C:\Program Files\PostgreSQL\16\data\`)
2. Change `md5` to `trust` for local connections
3. Restart PostgreSQL service
4. Connect without password: `psql -U postgres`
5. Change password: `ALTER USER postgres PASSWORD 'newpassword';`
6. Revert `pg_hba.conf` changes
7. Restart service

## Application Issues

### Issue: Port 8080 already in use
```
Port 8080 was already in use
```

**Solutions:**
```cmd
# Find what's using port 8080
netstat -ano | findstr :8080

# Kill the process (replace PID)
taskkill /PID <PID> /F

# Or change port in application.properties
server.port=8081
```

### Issue: Database connection refused
```
Connection to localhost:5432 refused
```

**Check list:**
1. PostgreSQL is running
2. Database `preply_auth` exists
3. Username/password are correct
4. Firewall isn't blocking port 5432

### Issue: JWT token errors
```
JWT signature does not match locally computed signature
```

**Solution:**
Ensure JWT_SECRET is set and is at least 32 characters:
```cmd
set JWT_SECRET=mySecretKey123456789012345678901234567890
```

## Testing Issues

### Issue: Swagger UI not loading
```
404 Not Found
```

**Solutions:**
1. Ensure application is running on port 8080
2. Check URL: `http://localhost:8080/api/swagger-ui.html`
3. Check logs for startup errors

### Issue: API tests failing
```
Connection refused
```

**Check:**
1. Application is running: `http://localhost:8080/api/swagger-ui.html`
2. No firewall blocking
3. Correct base URL in test script

## Environment Variables

### Setting permanently on Windows

#### Method 1: System Properties
1. Press `Win + R`, type `sysdm.cpl`
2. Click "Environment Variables"
3. Add under "User variables":
   - `DB_USERNAME=postgres`
   - `DB_PASSWORD=your_password`
   - `JWT_SECRET=mySecretKey123456789012345678901234567890`

#### Method 2: Command Line (session only)
```cmd
set DB_USERNAME=postgres
set DB_PASSWORD=your_password
set JWT_SECRET=mySecretKey123456789012345678901234567890
```

#### Method 3: PowerShell (session only)
```powershell
$env:DB_USERNAME="postgres"
$env:DB_PASSWORD="your_password"
$env:JWT_SECRET="mySecretKey123456789012345678901234567890"
```

## Quick Diagnostic Commands

### Check Java
```cmd
java -version
echo %JAVA_HOME%
```

### Check Maven
```cmd
mvn -version
echo %MAVEN_HOME%
```

### Check PostgreSQL
```cmd
psql --version
sc query postgresql-x64-16
```

### Check Docker
```cmd
docker --version
docker-compose --version
docker ps
```

### Check Ports
```cmd
netstat -ano | findstr :8080
netstat -ano | findstr :5432
```

### Check Application
```cmd
curl http://localhost:8080/api/swagger-ui.html
```

## Alternative Setup Methods

### Method 1: Docker (Recommended)
```cmd
start.bat
```

### Method 2: Manual with PostgreSQL
```cmd
setup-postgres.bat
start-manual.bat
```

### Method 3: H2 Database (Development only)
Edit `application.properties`:
```properties
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.hibernate.ddl-auto=create-drop
```

## Getting Help

If you're still having issues:

1. **Check logs:**
   ```cmd
   # Docker logs
   docker-compose logs auth-service
   
   # Application logs
   # Look in console output when running manually
   ```

2. **Verify prerequisites:**
   - Java 17+ installed
   - Maven 3.6+ installed (for manual setup)
   - Docker Desktop running (for Docker setup)
   - PostgreSQL 16 running (for manual setup)

3. **Try minimal setup:**
   Use H2 database for testing without PostgreSQL

4. **Contact support:**
   Provide error messages and system information
